<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\AccountType;

class DashboardController extends Controller
{
    public function index()
    {
        $account = Account::query()->where('id', auth()->id())->first();
        $tieuhocCourses = collect();
        $mamnonCourses = collect();

        // fetch course tieuhoc
        if($account->is_tieuhoc == 1){
            $tieuhocAccountTypeId = $account->tieuhoc_account_type_id;
            $tieuhocAccountType = AccountType::query()
                ->with('courses')
                ->where('id', $tieuhocAccountTypeId)->first();
            $tieuhocCourses = $tieuhocAccountType->courses;
        }

        // fetch course mamnon
        if($account->is_mamnon == 1){
            $mamnonAccountTypeId = $account->mamnon_account_type_id;
            $mamnonAccountType = AccountType::query()
                ->with('courses')
                ->where('id', $mamnonAccountTypeId)->first();
            $mamnonCourses = $mamnonAccountType->courses;
        }

        return view('dashboard', compact('account', 'tieuhocCourses', 'mamnonCourses'));
    }
}
