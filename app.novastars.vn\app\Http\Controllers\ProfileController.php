<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Students\Exception;
use App\Models\Account;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ProfileController extends Controller
{
    public function index()
    {
        $student_info = Account::query()->where('id', auth()->id())->first();
        return view('profile', compact('student_info'));
    }

    public function save_profile(Request $request)
    {
        try {
            $data = Account::query()->where('id', auth()->id())->first();

            $data->name = $request->name;
            $data->phone_number = $request->phone_number;
            $data->email = $request->email;
            $data->bio = $request->bio;
            $data->class = $request->class;

            if ($request->hasFile('image')) {
                $imageName = rand(111, 999) . time() . '.' . $request->image->extension();
                $request->image->move(public_path('uploads/students'), $imageName);
                $data->image = $imageName;
            }
            if ($data->save()) {
                return redirect()->back()->with('success', 'Your Changes Have been Saved');
            }
        } catch (Exception $e) {
            // dd($e);
            return redirect()->back()->withInput()->with('error', 'Something went wrong. Please try again');
        }
    }

    public function change_password(Request $request)
    {
        try {
            $data = Account::find(auth()->id());

            // Validate current password
            if (!Hash::check($request->current_password, $data->password)) {
                return redirect()->back()->with('error', 'Current password is incorrect.');
            }
            // Proceed with password change
            $data->password = Hash::make($request->password);

            if ($data->save()) {
                return redirect()->back()->with('success', 'Password Have been Changed');
            }
        } catch (Exception $e) {
            // dd($e);
            return redirect()->back()->withInput()->with('error', 'Something went wrong. Please try again');
        }
    }

    public function changeImage(Request $request)
    {
        try {
            $user = Account::find(auth()->id());

            if ($request->hasFile('image')) {
                $imageName = rand(111, 999) . time() . '.' . $request->image->extension();
                $request->image->move(public_path('uploads/students'), $imageName);
                $user->image = $imageName;
                $user->save();

                return redirect()->back()->with('success', 'Image changed successfully.');
            } else {
                return redirect()->back()->with('error', 'Please select a valid image file.');
            }
        } catch (\Exception $e) {
            // dd($e);
            return redirect()->back()->with('error', 'An error occurred. Please try again.');
        }
    }
}
