{"version": 3, "file": "database-extras.js", "sourceRoot": "", "sources": ["../../src/database-extras.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAGhD,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AACvE,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACvD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAgB,CAAC;AAE9C,SAAS,SAAS,CAChB,MAAW,EACX,IAA8B;IAE9B,IACE,CAAC,CACC,MAAM,YAAY,WAAW;QAC7B,CAAC,CAAC,IAAI,IAAI,MAAM,CAAC;QACjB,OAAO,IAAI,KAAK,QAAQ,CACzB,EACD;QACA,OAAO;KACR;IAED,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC;QAAE,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAE5D,MAAM,cAAc,GAAW,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;IAC9D,MAAM,QAAQ,GAAG,IAAI,KAAK,cAAc,CAAC;IACzC,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IAEtD;IACE,4EAA4E;IAC5E,CAAC,CAAC,cAAc,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC;QACrE,CAAC,CAAC,OAAO,IAAI,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAClD;QACA,OAAO;KACR;IAED,MAAM,MAAM,GAAG,KAAK,WAElB,SAAiB,EACjB,GAAG,IAAW;QAEd,wEAAwE;QACxE,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAC3E,IAAI,MAAM,GAAgC,EAAE,CAAC,KAAK,CAAC;QACnD,IAAI,QAAQ;YAAE,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,MAAO,MAAc,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QACjE,IAAI,OAAO;YAAE,MAAM,EAAE,CAAC,IAAI,CAAC;QAC3B,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC;IAEF,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAChC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,YAAY,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC1B,GAAG,QAAQ;IACX,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,CAC9B,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,QAAQ,CAAC,GAAI,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC;IAClE,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CACpB,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,QAAQ,CAAC,GAAI,CAAC,MAAM,EAAE,IAAI,CAAC;CAC3D,CAAC,CAAC,CAAC"}