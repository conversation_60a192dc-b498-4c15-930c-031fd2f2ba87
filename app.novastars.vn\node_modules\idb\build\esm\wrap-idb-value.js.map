{"version": 3, "file": "wrap-idb-value.js", "sourceRoot": "", "sources": ["../../src/wrap-idb-value.ts"], "names": [], "mappings": "AAQA,OAAO,EAAqB,aAAa,EAAE,MAAM,QAAQ,CAAC;AAE1D,IAAI,iBAAgC,CAAC;AACrC,IAAI,oBAA4B,CAAC;AAEjC,qEAAqE;AACrE,SAAS,oBAAoB;IAC3B,OAAO,CACL,iBAAiB;QACjB,CAAC,iBAAiB,GAAG;YACnB,WAAW;YACX,cAAc;YACd,QAAQ;YACR,SAAS;YACT,cAAc;SACf,CAAC,CACH,CAAC;AACJ,CAAC;AAED,qEAAqE;AACrE,SAAS,uBAAuB;IAC9B,OAAO,CACL,oBAAoB;QACpB,CAAC,oBAAoB,GAAG;YACtB,SAAS,CAAC,SAAS,CAAC,OAAO;YAC3B,SAAS,CAAC,SAAS,CAAC,QAAQ;YAC5B,SAAS,CAAC,SAAS,CAAC,kBAAkB;SACvC,CAAC,CACH,CAAC;AACJ,CAAC;AAED,MAAM,gBAAgB,GAGlB,IAAI,OAAO,EAAE,CAAC;AAClB,MAAM,kBAAkB,GAGpB,IAAI,OAAO,EAAE,CAAC;AAClB,MAAM,wBAAwB,GAG1B,IAAI,OAAO,EAAE,CAAC;AAClB,MAAM,cAAc,GAAG,IAAI,OAAO,EAAE,CAAC;AACrC,MAAM,CAAC,MAAM,qBAAqB,GAAG,IAAI,OAAO,EAAE,CAAC;AAEnD,SAAS,gBAAgB,CAAI,OAAsB;IACjD,MAAM,OAAO,GAAG,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACjD,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,OAAO,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAChD,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC;QACF,MAAM,OAAO,GAAG,GAAG,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAa,CAAQ,CAAC,CAAC;YAC5C,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC;QACF,MAAM,KAAK,GAAG,GAAG,EAAE;YACjB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACtB,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC;QACF,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC7C,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,OAAO;SACJ,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACd,kFAAkF;QAClF,sBAAsB;QACtB,IAAI,KAAK,YAAY,SAAS,EAAE;YAC9B,gBAAgB,CAAC,GAAG,CACjB,KAA+B,EAC/B,OAA4C,CAC9C,CAAC;SACH;QACD,kDAAkD;IACpD,CAAC,CAAC;SACD,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAEnB,iGAAiG;IACjG,+DAA+D;IAC/D,qBAAqB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5C,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,8BAA8B,CAAC,EAAkB;IACxD,2EAA2E;IAC3E,IAAI,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC;QAAE,OAAO;IAEvC,MAAM,IAAI,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACjD,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,EAAE,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC7C,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACvC,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC;QACF,MAAM,KAAK,GAAG,GAAG,EAAE;YACjB,MAAM,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC;YACjE,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC;QACF,EAAE,CAAC,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC1C,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,gCAAgC;IAChC,kBAAkB,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC;AAED,IAAI,aAAa,GAAsB;IACrC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ;QACxB,IAAI,MAAM,YAAY,cAAc,EAAE;YACpC,yCAAyC;YACzC,IAAI,IAAI,KAAK,MAAM;gBAAE,OAAO,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC3D,iDAAiD;YACjD,IAAI,IAAI,KAAK,kBAAkB,EAAE;gBAC/B,OAAO,MAAM,CAAC,gBAAgB,IAAI,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;aACxE;YACD,0FAA0F;YAC1F,IAAI,IAAI,KAAK,OAAO,EAAE;gBACpB,OAAO,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC;oBACjC,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;aACxD;SACF;QACD,uCAAuC;QACvC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK;QACrB,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,GAAG,CAAC,MAAM,EAAE,IAAI;QACd,IACE,MAAM,YAAY,cAAc;YAChC,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,OAAO,CAAC,EACrC;YACA,OAAO,IAAI,CAAC;SACb;QACD,OAAO,IAAI,IAAI,MAAM,CAAC;IACxB,CAAC;CACF,CAAC;AAEF,MAAM,UAAU,YAAY,CAC1B,QAAgE;IAEhE,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,YAAY,CAAiB,IAAO;IAC3C,mFAAmF;IACnF,qCAAqC;IAErC,wEAAwE;IACxE,IACE,IAAI,KAAK,WAAW,CAAC,SAAS,CAAC,WAAW;QAC1C,CAAC,CAAC,kBAAkB,IAAI,cAAc,CAAC,SAAS,CAAC,EACjD;QACA,OAAO,UAEL,UAA6B,EAC7B,GAAG,IAAW;YAEd,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC;YACxD,wBAAwB,CAAC,GAAG,CAC1B,EAAE,EACD,UAAkB,CAAC,IAAI,CAAC,CAAC,CAAE,UAAoB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CACvE,CAAC;YACF,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC;KACH;IAED,8FAA8F;IAC9F,+FAA+F;IAC/F,+FAA+F;IAC/F,8FAA8F;IAC9F,uDAAuD;IACvD,IAAI,uBAAuB,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC5C,OAAO,UAA4B,GAAG,IAAmB;YACvD,8FAA8F;YAC9F,uBAAuB;YACvB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;YAC/B,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,CAAC;QAC3C,CAAC,CAAC;KACH;IAED,OAAO,UAAqB,GAAG,IAAmB;QAChD,8FAA8F;QAC9F,uBAAuB;QACvB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,CAAC,KAAU;IACxC,IAAI,OAAO,KAAK,KAAK,UAAU;QAAE,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC;IAE5D,6EAA6E;IAC7E,uEAAuE;IACvE,IAAI,KAAK,YAAY,cAAc;QAAE,8BAA8B,CAAC,KAAK,CAAC,CAAC;IAE3E,IAAI,aAAa,CAAC,KAAK,EAAE,oBAAoB,EAAE,CAAC;QAC9C,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;IAEzC,iEAAiE;IACjE,OAAO,KAAK,CAAC;AACf,CAAC;AAeD,MAAM,UAAU,IAAI,CAAC,KAAU;IAC7B,gGAAgG;IAChG,2FAA2F;IAC3F,IAAI,KAAK,YAAY,UAAU;QAAE,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAEhE,+EAA+E;IAC/E,wDAAwD;IACxD,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC;QAAE,OAAO,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAChE,MAAM,QAAQ,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAE/C,iCAAiC;IACjC,+DAA+D;IAC/D,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACpC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC5C;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAoBD,MAAM,CAAC,MAAM,MAAM,GAAW,CAAC,KAAU,EAAO,EAAE,CAChD,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC"}