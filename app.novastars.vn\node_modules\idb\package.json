{"name": "idb", "version": "5.0.8", "description": "A small wrapper that makes IndexedDB usable", "main": "build/cjs/index.js", "module": "build/esm/index.js", "typings": "build/esm/index.d.ts", "scripts": {"build": "PRODUCTION=1 rollup -c && node --experimental-modules lib/size-report.mjs", "dev": "rollup -c --watch"}, "repository": {"type": "git", "url": "git://github.com/jakearchibald/idb.git"}, "files": ["build/**", "with-*"], "author": "<PERSON>", "license": "ISC", "devDependencies": {"@rollup/plugin-commonjs": "^12.0.0", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "chai": "^4.2.0", "conditional-type-checks": "^1.0.5", "del": "^5.1.0", "filesize": "^6.1.0", "glob": "^7.1.6", "mocha": "^7.2.0", "prettier": "^2.0.5", "rollup": "^2.10.9", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^6.1.0", "typescript": "^3.9.3"}}