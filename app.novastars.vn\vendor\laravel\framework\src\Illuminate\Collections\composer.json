{"name": "illuminate/collections", "description": "The Illuminate Collections package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "illuminate/conditionable": "^10.0", "illuminate/contracts": "^10.0", "illuminate/macroable": "^10.0"}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["helpers.php"]}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^6.2)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}