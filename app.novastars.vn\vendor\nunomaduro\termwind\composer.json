{"name": "nunomaduro/termwind", "description": "Its like Tailwind CSS, but for the console.", "keywords": ["php", "cli", "package", "console", "css", "style"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0", "ext-mbstring": "*", "symfony/console": "^5.3.0|^6.0.0"}, "require-dev": {"ergebnis/phpstan-rules": "^1.0.", "illuminate/console": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "laravel/pint": "^1.0.0", "pestphp/pest": "^1.21.0", "pestphp/pest-plugin-mock": "^1.0", "phpstan/phpstan": "^1.4.6", "phpstan/phpstan-strict-rules": "^1.1.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}, "autoload": {"psr-4": {"Termwind\\": "src/"}, "files": ["src/Functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true, "preferred-install": "dist", "allow-plugins": {"pestphp/pest-plugin": true}}, "scripts": {"lint": "pint -v", "test:lint": "pint --test -v", "test:types": "phpstan analyse --ansi", "test:unit": "pest --colors=always", "test": ["@test:lint", "@test:types", "@test:unit"]}, "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}}}